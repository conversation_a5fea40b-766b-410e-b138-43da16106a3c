using { /Fortnite.com/Devices }
using { /Verse.org/Simulation }
using { /UnrealEngine.com/Temporary/Diagnostics }
using { /Fortnite.com/Characters }
using { /Fortnite.com/Game }

# Import our game types and interfaces
using { /<EMAIL>/TheTiltedShuffle/game_types }
using { /<EMAIL>/TheTiltedShuffle/mode_controller }

# Buildless Box Fights with Loadout Draft mode controller
# Implements tactical combat with strategic weapon selection and building disabled
box_fight_controller := class(mode_controller):
    
    # Required devices for box fight mode
    var PlayerSpawners: []player_spawner_device = array{}
    var ItemGranters: []item_granter_device = array{}
    var BuildingDisabler: ?building_disabler_device = false
    var RoundTimer: ?timer_device = false
    var DraftInterface: ?ui_device = false
    
    # Mode state tracking
    var IsActive: logic = false
    var ActivePlayers: []player = array{}
    var PlayerWins: [player]int = map{}
    var CurrentRound: int = 0
    var MaxRounds: int = 9  # Best of 9
    var WinsToWin: int = 5  # First to 5 wins
    var ModeConfig: mode_config = mode_config{}
    
    # Draft system state
    var IsDrafting: logic = false
    var DraftOrder: []player = array{}
    var CurrentDraftPlayer: int = 0
    var DraftRound: int = 0
    var MaxDraftRounds: int = 3  # 3 items per player
    var PlayerLoadouts: [player][]draft_item = map{}
    var AvailableItems: []draft_item = array{}
    var DraftedItems: []draft_item = array{}
    
    # Round state tracking
    var RoundActive: logic = false
    var RoundWinner: ?player = false
    var RoundTimeRemaining: float = 300.0  # 5 minutes per round
    var RoundStartTime: float = 0.0
    
    # Draft item structure
    draft_item := struct:
        ItemName: string
        ItemType: string  # "weapon", "utility", "healing"
        Rarity: string    # "common", "uncommon", "rare", "epic", "legendary"
        IsAvailable: logic
    
    # Draft pool configuration (shared pool for all players)
    var DraftPool: []draft_item = array{
        # Weapons
        draft_item{ItemName := "Assault Rifle", ItemType := "weapon", Rarity := "uncommon", IsAvailable := true},
        draft_item{ItemName := "Shotgun", ItemType := "weapon", Rarity := "rare", IsAvailable := true},
        draft_item{ItemName := "SMG", ItemType := "weapon", Rarity := "uncommon", IsAvailable := true},
        draft_item{ItemName := "Sniper Rifle", ItemType := "weapon", Rarity := "epic", IsAvailable := true},
        draft_item{ItemName := "Pistol", ItemType := "weapon", Rarity := "common", IsAvailable := true},
        draft_item{ItemName := "Rocket Launcher", ItemType := "weapon", Rarity := "legendary", IsAvailable := true},
        
        # Utilities
        draft_item{ItemName := "Grenades", ItemType := "utility", Rarity := "uncommon", IsAvailable := true},
        draft_item{ItemName := "Shield Potion", ItemType := "utility", Rarity := "rare", IsAvailable := true},
        draft_item{ItemName := "Impulse Grenades", ItemType := "utility", Rarity := "rare", IsAvailable := true},
        draft_item{ItemName := "Smoke Grenades", ItemType := "utility", Rarity := "uncommon", IsAvailable := true},
        draft_item{ItemName := "Stink Bombs", ItemType := "utility", Rarity := "rare", IsAvailable := true},
        draft_item{ItemName := "Shockwave Grenades", ItemType := "utility", Rarity := "epic", IsAvailable := true},
        
        # Healing
        draft_item{ItemName := "Bandages", ItemType := "healing", Rarity := "common", IsAvailable := true},
        draft_item{ItemName := "Med Kit", ItemType := "healing", Rarity := "uncommon", IsAvailable := true},
        draft_item{ItemName := "Mini Shield", ItemType := "healing", Rarity := "uncommon", IsAvailable := true},
        draft_item{ItemName := "Big Shield", ItemType := "healing", Rarity := "rare", IsAvailable := true},
        draft_item{ItemName := "Slurp Juice", ItemType := "healing", Rarity := "epic", IsAvailable := true},
        draft_item{ItemName := "Chug Jug", ItemType := "healing", Rarity := "legendary", IsAvailable := true}
    }
    
    # Initialize the box fight controller
    Initialize<override>(): void =
        Print("Initializing Buildless Box Fight Controller...")
        
        # Set up mode configuration
        set ModeConfig = mode_config{
            RequiredDevices := array{"spawner", "granter", "building_disabler", "timer", "ui_device"},
            SpawnConfiguration := "box_fight_arena_positions",
            ItemLoadouts := array{"draft_selected_loadouts"},
            SpecialRules := array{"building_disabled", "snake_draft", "best_of_9", "5_wins_to_win"},
            TimerSettings := "5_minutes_per_round"
        }
        
        # Initialize state
        set IsActive = false
        set ActivePlayers = array{}
        set PlayerWins = map{}
        set CurrentRound = 0
        set IsDrafting = false
        set RoundActive = false
        set RoundWinner = false
        
        # Initialize draft system
        InitializeDraftSystem()
        
        Print("Buildless Box Fight Controller initialized successfully")
    
    # Start the box fight mode
    StartMode<override>(): void =
        Print("Starting Buildless Box Fight mode...")
        
        # Validate required devices are available
        if (not ValidateRequiredDevices()):
            Print("Cannot start Box Fight - required devices not available")
            return
        
        # Set mode as active
        set IsActive = true
        
        # Configure devices for box fight
        ConfigureDevicesForBoxFight()
        
        # Disable building mechanics
        DisableBuildingMechanics()
        
        # Initialize player tracking
        InitializePlayerTracking()
        
        # Start draft phase
        StartDraftPhase()
        
        Print("Buildless Box Fight mode started successfully")
    
    # End the box fight mode
    EndMode<override>(): void =
        Print("Ending Buildless Box Fight mode...")
        
        # Set mode as inactive
        set IsActive = false
        
        # End current round if active
        if (RoundActive):
            EndCurrentRound()
        
        # End draft if active
        if (IsDrafting):
            EndDraftPhase()
        
        # Re-enable building mechanics
        EnableBuildingMechanics()
        
        # Reset device configurations
        ResetDeviceConfigurations()
        
        # Clear all state
        ClearAllState()
        
        Print("Buildless Box Fight mode ended successfully")
    
    # Handle player elimination in box fight
    HandlePlayerElimination<override>(Player: player): void =
        Print("Player eliminated in Box Fight: {Player}")
        
        # Only handle eliminations during active rounds
        if (not RoundActive):
            Print("Elimination ignored - no active round")
            return
        
        # Check if this elimination ends the round
        var RemainingPlayers: []player = GetRemainingPlayersInRound()
        
        if (RemainingPlayers.Length <= 1):
            # Round ends - determine winner
            if (RemainingPlayers.Length = 1):
                if (Winner := RemainingPlayers[0]):
                    HandleRoundWin(Winner)
            else:
                # No players remaining - draw
                HandleRoundDraw()
        
        Print("Elimination handled in Box Fight mode")
    
    # Get mode-specific devices required for box fight
    GetModeSpecificDevices<override>(): []creative_device =
        var Devices: []creative_device = array{}
        
        # Add player spawners
        for (Spawner : PlayerSpawners):
            set Devices += array{Spawner}
        
        # Add item granters
        for (Granter : ItemGranters):
            set Devices += array{Granter}
        
        # Add building disabler if available
        if (Disabler := BuildingDisabler?):
            set Devices += array{Disabler}
        
        # Add round timer if available
        if (Timer := RoundTimer?):
            set Devices += array{Timer}
        
        # Add draft interface if available
        if (Interface := DraftInterface?):
            set Devices += array{Interface}
        
        Devices
    
    # Get current mode configuration
    GetModeConfig<override>(): mode_config = ModeConfig
    
    # Handle player joining during active box fight mode
    HandlePlayerJoin<override>(Player: player): void =
        Print("Player joined during Box Fight: {Player}")
        
        # Only allow joins if mode is active but not mid-round
        if (not IsActive):
            Print("Cannot join - Box Fight mode not active")
            return
        
        if (RoundActive):
            Print("Cannot join - round in progress")
            return
        
        if (IsDrafting):
            Print("Cannot join - draft in progress")
            return
        
        # Add to active players
        set ActivePlayers += array{Player}
        
        # Initialize player win tracking
        set PlayerWins = PlayerWins + map{Player => 0}
        
        Print("Player successfully joined Box Fight mode")
    
    # Handle player leaving during active box fight mode
    HandlePlayerLeave<override>(Player: player): void =
        Print("Player left during Box Fight: {Player}")
        
        # Remove from active players
        if (PlayerIndex := ActivePlayers.FindIndex[Player]):
            set ActivePlayers = ActivePlayers.RemoveElement[PlayerIndex]
        
        # Remove from win tracking
        if (PlayerWins.HasKey[Player]):
            set PlayerWins = PlayerWins.RemoveKey[Player]
        
        # Handle draft implications
        if (IsDrafting):
            HandlePlayerLeaveFromDraft(Player)
        
        # Handle round implications
        if (RoundActive):
            HandlePlayerLeaveFromRound(Player)
        
        # Check if win condition is now met
        if (CheckWinCondition()):
            Print("Win condition met after player leave - ending Box Fight mode")
    
    # Check if box fight win condition is met (player has 5 wins)
    CheckWinCondition<override>(): logic =
        if (not IsActive):
            return false
        
        # Win condition: player has reached 5 wins
        for (Player : ActivePlayers):
            if (Wins := PlayerWins[Player]):
                if (Wins >= WinsToWin):
                    return true
        
        # Win condition: not enough players to continue
        if (ActivePlayers.Length < 2):
            return true
        
        return false
    
    # Get current mode status for HUD display
    GetModeStatus<override>(): string =
        if (not IsActive):
            return "Box Fight: Inactive"
        
        if (IsDrafting):
            return GetDraftStatus()
        
        if (RoundActive):
            return GetRoundStatus()
        
        return GetOverallStatus()    

    # Validate that all required devices are available
    ValidateRequiredDevices(): logic =
        var IsValid: logic = true
        
        # Check player spawners
        if (PlayerSpawners.Length <= 0):
            Print("Box Fight validation failed: No player spawners available")
            set IsValid = false
        
        # Check item granters
        if (ItemGranters.Length <= 0):
            Print("Box Fight validation failed: No item granters available")
            set IsValid = false
        
        # Building disabler is required for buildless mode
        if (not BuildingDisabler?):
            Print("Box Fight validation failed: No building disabler available")
            set IsValid = false
        
        # Round timer is required for round management
        if (not RoundTimer?):
            Print("Box Fight validation failed: No round timer available")
            set IsValid = false
        
        IsValid
    
    # Configure devices specifically for box fight mode
    ConfigureDevicesForBoxFight(): void =
        Print("Configuring devices for Box Fight mode...")
        
        # Configure spawners for box fight arena positions
        ConfigureSpawnersForBoxFightArena()
        
        # Configure item granters for draft-based loadouts
        ConfigureItemGrantersForDraftLoadouts()
        
        # Configure building disabler
        ConfigureBuildingDisabler()
        
        # Configure round timer
        ConfigureRoundTimer()
        
        Print("Device configuration for Box Fight complete")
    
    # Configure spawners for box fight arena positions
    ConfigureSpawnersForBoxFightArena(): void =
        Print("Configuring spawners for box fight arena...")
        
        # Set spawners to use box fight arena spawn points
        for (Spawner : PlayerSpawners):
            # Configure spawner for box fight arena positioning
            # Specific spawner configuration will depend on UEFN device API
            Print("Configured spawner for box fight arena")
    
    # Configure item granters for draft-based loadouts
    ConfigureItemGrantersForDraftLoadouts(): void =
        Print("Configuring item granters for draft loadouts...")
        
        # Configure granters to provide draft-selected items
        for (Granter : ItemGranters):
            # Configure granter for draft-based item distribution
            # Specific granter configuration will depend on UEFN device API
            Print("Configured item granter for draft loadouts")
    
    # Configure building disabler device
    ConfigureBuildingDisabler(): void =
        Print("Configuring building disabler...")
        
        if (Disabler := BuildingDisabler?):
            # Configure building disabler for buildless gameplay
            # Specific disabler configuration will depend on UEFN device API
            Print("Configured building disabler")
        else:
            Print("No building disabler available for configuration")
    
    # Configure round timer for 5-minute rounds
    ConfigureRoundTimer(): void =
        Print("Configuring round timer for 5-minute rounds...")
        
        if (Timer := RoundTimer?):
            # Configure timer for 5-minute round duration
            # Specific timer configuration will depend on UEFN device API
            Print("Configured round timer for 5-minute rounds")
        else:
            Print("No round timer available for configuration")
    
    # Disable building mechanics for buildless gameplay
    DisableBuildingMechanics(): void =
        Print("Disabling building mechanics...")
        
        if (Disabler := BuildingDisabler?):
            # Activate building disabler to prevent building
            # Specific building disable logic will depend on UEFN device API
            Print("Building mechanics disabled")
        else:
            Print("No building disabler available")
    
    # Re-enable building mechanics
    EnableBuildingMechanics(): void =
        Print("Re-enabling building mechanics...")
        
        if (Disabler := BuildingDisabler?):
            # Deactivate building disabler to restore building
            # Specific building enable logic will depend on UEFN device API
            Print("Building mechanics re-enabled")
        else:
            Print("No building disabler available")
    
    # Initialize player tracking for wins and rounds
    InitializePlayerTracking(): void =
        Print("Initializing player tracking...")
        
        # Initialize win tracking for all active players
        for (Player : ActivePlayers):
            set PlayerWins = PlayerWins + map{Player => 0}
        
        # Reset round tracking
        set CurrentRound = 0
        set RoundActive = false
        set RoundWinner = false
        
        Print("Player tracking initialized")
    
    # Initialize the draft system
    InitializeDraftSystem(): void =
        Print("Initializing draft system...")
        
        # Reset draft state
        set IsDrafting = false
        set DraftOrder = array{}
        set CurrentDraftPlayer = 0
        set DraftRound = 0
        set PlayerLoadouts = map{}
        set DraftedItems = array{}
        
        # Reset available items from draft pool
        set AvailableItems = array{}
        for (Item : DraftPool):
            set AvailableItems += array{draft_item{
                ItemName := Item.ItemName,
                ItemType := Item.ItemType,
                Rarity := Item.Rarity,
                IsAvailable := true
            }}
        
        Print("Draft system initialized")
    
    # Start the draft phase
    StartDraftPhase(): void =
        Print("Starting draft phase...")
        
        # Validate we have enough players
        if (ActivePlayers.Length < 2):
            Print("Cannot start draft - need at least 2 players")
            return
        
        # Set draft as active
        set IsDrafting = true
        
        # Create snake draft order
        CreateSnakeDraftOrder()
        
        # Initialize player loadouts
        for (Player : ActivePlayers):
            set PlayerLoadouts = PlayerLoadouts + map{Player => array{}}
        
        # Start first draft round
        set DraftRound = 1
        set CurrentDraftPlayer = 0
        
        # Begin draft with first player
        BeginPlayerDraftTurn()
        
        Print("Draft phase started with snake draft order")
    
    # Create snake draft order (1-2-3-3-2-1 pattern)
    CreateSnakeDraftOrder(): void =
        Print("Creating snake draft order...")
        
        # Clear existing draft order
        set DraftOrder = array{}
        
        # Create snake pattern for 3 rounds of drafting
        for (Round := 1; Round <= MaxDraftRounds; Round += 1):
            if (Round % 2 = 1):
                # Odd rounds: normal order (1-2-3)
                for (Player : ActivePlayers):
                    set DraftOrder += array{Player}
            else:
                # Even rounds: reverse order (3-2-1)
                var ReversedPlayers: []player = array{}
                for (I := ActivePlayers.Length - 1; I >= 0; I -= 1):
                    if (Player := ActivePlayers[I]):
                        set ReversedPlayers += array{Player}
                
                for (Player : ReversedPlayers):
                    set DraftOrder += array{Player}
        
        Print("Snake draft order created with {DraftOrder.Length} total picks")
    
    # Begin a player's draft turn
    BeginPlayerDraftTurn(): void =
        if (CurrentDraftPlayer >= DraftOrder.Length):
            # Draft complete
            CompleteDraftPhase()
            return
        
        if (DraftingPlayer := DraftOrder[CurrentDraftPlayer]):
            Print("Beginning draft turn for player: {DraftingPlayer}")
            
            # Present available items to drafting player
            PresentDraftOptions(DraftingPlayer)
        else:
            Print("Error: Invalid drafting player at index {CurrentDraftPlayer}")
            AdvanceDraftTurn()
    
    # Present draft options to the current drafting player
    PresentDraftOptions(DraftingPlayer: player): void =
        Print("Presenting draft options to {DraftingPlayer}...")
        
        # Filter available items
        var AvailableOptions: []draft_item = array{}
        for (Item : AvailableItems):
            if (Item.IsAvailable):
                set AvailableOptions += array{Item}
        
        if (AvailableOptions.Length <= 0):
            Print("No items available for draft - ending draft early")
            CompleteDraftPhase()
            return
        
        # Present options via UI (implementation depends on UEFN UI system)
        DisplayDraftOptionsToPlayer(DraftingPlayer, AvailableOptions)
        
        Print("Draft options presented to {DraftingPlayer}")
    
    # Display draft options to player via UI
    DisplayDraftOptionsToPlayer(DraftingPlayer: player, Options: []draft_item): void =
        Print("Displaying {Options.Length} draft options to {DraftingPlayer}")
        
        if (Interface := DraftInterface?):
            # Display draft options using UI device
            # Specific UI logic will depend on UEFN device API
            Print("Draft options displayed via UI device")
        else:
            # Fallback: display via HUD messages
            var OptionsText: string = "Draft Options: "
            for (I := 0; I < Options.Length; I += 1):
                if (Option := Options[I]):
                    set OptionsText = OptionsText + "{I+1}. {Option.ItemName} "
            
            # Display options text to player
            Print("Draft options: {OptionsText}")
    
    # Handle player draft selection
    HandleDraftSelection(DraftingPlayer: player, SelectedItem: draft_item): void =
        Print("Player {DraftingPlayer} selected: {SelectedItem.ItemName}")
        
        # Validate selection
        if (not SelectedItem.IsAvailable):
            Print("Invalid selection - item not available")
            return
        
        # Add item to player's loadout
        if (CurrentLoadout := PlayerLoadouts[DraftingPlayer]):
            set PlayerLoadouts = PlayerLoadouts + map{DraftingPlayer => CurrentLoadout + array{SelectedItem}}
        else:
            set PlayerLoadouts = PlayerLoadouts + map{DraftingPlayer => array{SelectedItem}}
        
        # Mark item as unavailable
        MarkItemAsUnavailable(SelectedItem)
        
        # Add to drafted items list
        set DraftedItems += array{SelectedItem}
        
        # Advance to next draft turn
        AdvanceDraftTurn()
        
        Print("Draft selection processed for {DraftingPlayer}")
    
    # Mark an item as unavailable in the draft pool
    MarkItemAsUnavailable(SelectedItem: draft_item): void =
        for (I := 0; I < AvailableItems.Length; I += 1):
            if (Item := AvailableItems[I]):
                if (Item.ItemName = SelectedItem.ItemName):
                    set AvailableItems = AvailableItems.RemoveElement[I]
                    break
        
        Print("Item {SelectedItem.ItemName} marked as unavailable")
    
    # Advance to the next draft turn
    AdvanceDraftTurn(): void =
        set CurrentDraftPlayer = CurrentDraftPlayer + 1
        
        # Check if draft round is complete
        if (CurrentDraftPlayer >= DraftOrder.Length):
            CompleteDraftPhase()
        else:
            BeginPlayerDraftTurn()
    
    # Complete the draft phase and start first round
    CompleteDraftPhase(): void =
        Print("Completing draft phase...")
        
        # Set draft as inactive
        set IsDrafting = false
        
        # Grant drafted loadouts to all players
        GrantDraftedLoadouts()
        
        # Start first round
        StartNextRound()
        
        Print("Draft phase completed - starting first round")
    
    # Grant drafted loadouts to all players
    GrantDraftedLoadouts(): void =
        Print("Granting drafted loadouts to players...")
        
        for (Player : ActivePlayers):
            if (Loadout := PlayerLoadouts[Player]):
                GrantLoadoutToPlayer(Player, Loadout)
        
        Print("All drafted loadouts granted")
    
    # Grant specific loadout to a player
    GrantLoadoutToPlayer(Player: player, Loadout: []draft_item): void =
        Print("Granting loadout to {Player}...")
        
        for (Item : Loadout):
            GrantItemToPlayer(Player, Item)
        
        Print("Loadout granted to {Player}")
    
    # Grant specific item to a player
    GrantItemToPlayer(Player: player, Item: draft_item): void =
        Print("Granting {Item.ItemName} to {Player}")
        
        # Use item granters to give specific item to player
        for (Granter : ItemGranters):
            # Configure granter for specific item and player
            # Specific item granting logic will depend on UEFN device API
            Print("Item {Item.ItemName} granted to {Player}")
            break  # Use first available granter
    
    # Start the next round
    StartNextRound(): void =
        set CurrentRound = CurrentRound + 1
        Print("Starting round {CurrentRound}...")
        
        # Validate we can start a round
        if (ActivePlayers.Length < 2):
            Print("Cannot start round - not enough players")
            return
        
        # Set round as active
        set RoundActive = true
        set RoundWinner = false
        set RoundTimeRemaining = 300.0  # 5 minutes
        
        # Spawn players in box fight arena
        SpawnPlayersForRound()
        
        # Start round timer
        StartRoundTimer()
        
        Print("Round {CurrentRound} started")
    
    # Spawn players for the current round
    SpawnPlayersForRound(): void =
        Print("Spawning players for round {CurrentRound}...")
        
        for (Player : ActivePlayers):
            SpawnPlayerInArena(Player)
        
        Print("All players spawned for round")
    
    # Spawn a specific player in the box fight arena
    SpawnPlayerInArena(Player: player): void =
        Print("Spawning {Player} in box fight arena")
        
        # Use spawners to place player in arena
        if (PlayerSpawners.Length > 0):
            var SpawnerIndex: int = GetRandomInt(0, PlayerSpawners.Length - 1)
            if (Spawner := PlayerSpawners[SpawnerIndex]):
                # Spawn player using selected spawner
                # Specific spawning logic will depend on UEFN device API
                Print("Player {Player} spawned in arena")
        else:
            Print("No spawners available for arena spawning")
    
    # Start the round timer
    StartRoundTimer(): void =
        Print("Starting round timer for 5 minutes...")
        
        if (Timer := RoundTimer?):
            # Start timer for 5-minute round duration
            # Specific timer logic will depend on UEFN device API
            Print("Round timer started")
        else:
            Print("No round timer available")
    
    # Handle round win by a player
    HandleRoundWin(Winner: player): void =
        Print("Round {CurrentRound} won by {Winner}")
        
        # Set round winner
        set RoundWinner = Winner
        
        # Increment player's win count
        if (CurrentWins := PlayerWins[Winner]):
            set PlayerWins = PlayerWins + map{Winner => CurrentWins + 1}
        else:
            set PlayerWins = PlayerWins + map{Winner => 1}
        
        # End current round
        EndCurrentRound()
        
        # Check if overall win condition is met
        if (CheckWinCondition()):
            Print("Overall win condition met - {Winner} wins the match!")
            return
        
        # Check if we've reached max rounds
        if (CurrentRound >= MaxRounds):
            HandleMaxRoundsReached()
            return
        
        # Start next round after delay
        StartNextRound()
    
    # Handle round draw (no winner)
    HandleRoundDraw(): void =
        Print("Round {CurrentRound} ended in a draw")
        
        # End current round without winner
        EndCurrentRound()
        
        # Check if we've reached max rounds
        if (CurrentRound >= MaxRounds):
            HandleMaxRoundsReached()
            return
        
        # Start next round after delay
        StartNextRound()
    
    # End the current round
    EndCurrentRound(): void =
        Print("Ending round {CurrentRound}...")
        
        # Set round as inactive
        set RoundActive = false
        
        # Stop round timer
        StopRoundTimer()
        
        Print("Round {CurrentRound} ended")
    
    # Stop the round timer
    StopRoundTimer(): void =
        Print("Stopping round timer...")
        
        if (Timer := RoundTimer?):
            # Stop the round timer
            # Specific timer logic will depend on UEFN device API
            Print("Round timer stopped")
    
    # Handle when maximum rounds are reached
    HandleMaxRoundsReached(): void =
        Print("Maximum rounds ({MaxRounds}) reached")
        
        # Determine overall winner based on most wins
        var HighestWins: int = 0
        var OverallWinner: ?player = false
        var IsTied: logic = false
        
        for (Player : ActivePlayers):
            if (Wins := PlayerWins[Player]):
                if (Wins > HighestWins):
                    set HighestWins = Wins
                    set OverallWinner = Player
                    set IsTied = false
                else if (Wins = HighestWins):
                    set IsTied = true
        
        if (IsTied):
            Print("Match ended in a tie after {MaxRounds} rounds")
        else if (Winner := OverallWinner?):
            Print("Overall winner: {Winner} with {HighestWins} wins")
        else:
            Print("No clear winner determined")
    
    # Get remaining players in current round
    GetRemainingPlayersInRound(): []player =
        # In actual implementation, would check player elimination status
        # For now, return all active players as placeholder
        return ActivePlayers
    
    # Handle player leaving from draft
    HandlePlayerLeaveFromDraft(Player: player): void =
        Print("Handling player leave from draft: {Player}")
        
        # Remove from draft order if present
        var NewDraftOrder: []player = array{}
        for (DraftPlayer : DraftOrder):
            if (DraftPlayer <> Player):
                set NewDraftOrder += array{DraftPlayer}
        set DraftOrder = NewDraftOrder
        
        # Adjust current draft player index if needed
        if (CurrentDraftPlayer >= DraftOrder.Length):
            set CurrentDraftPlayer = 0
        
        # Remove from player loadouts
        if (PlayerLoadouts.HasKey[Player]):
            set PlayerLoadouts = PlayerLoadouts.RemoveKey[Player]
        
        Print("Player removed from draft")
    
    # Handle player leaving from active round
    HandlePlayerLeaveFromRound(Player: player): void =
        Print("Handling player leave from round: {Player}")
        
        # Check if this affects round outcome
        var RemainingPlayers: []player = GetRemainingPlayersInRound()
        
        if (RemainingPlayers.Length <= 1):
            # Round may end due to insufficient players
            if (RemainingPlayers.Length = 1):
                if (Winner := RemainingPlayers[0]):
                    HandleRoundWin(Winner)
            else:
                HandleRoundDraw()
        
        Print("Player leave from round handled")
    
    # Get draft status for HUD display
    GetDraftStatus(): string =
        if (CurrentDraftPlayer < DraftOrder.Length):
            if (DraftingPlayer := DraftOrder[CurrentDraftPlayer]):
                return "Box Fight: {DraftingPlayer} drafting (Round {DraftRound})"
        
        return "Box Fight: Draft in progress"
    
    # Get round status for HUD display
    GetRoundStatus(): string =
        var TimeLeft: int = Floor(RoundTimeRemaining)
        var PlayersAlive: int = GetRemainingPlayersInRound().Length
        
        return "Box Fight: Round {CurrentRound}, {PlayersAlive} alive, {TimeLeft}s"
    
    # Get overall status for HUD display
    GetOverallStatus(): string =
        var LeaderWins: int = 0
        var Leader: ?player = false
        
        for (Player : ActivePlayers):
            if (Wins := PlayerWins[Player]):
                if (Wins > LeaderWins):
                    set LeaderWins = Wins
                    set Leader = Player
        
        if (LeaderPlayer := Leader?):
            return "Box Fight: {LeaderPlayer} leads with {LeaderWins} wins"
        
        return "Box Fight: Waiting for next round"
    
    # Reset all device configurations to default state
    ResetDeviceConfigurations(): void =
        Print("Resetting device configurations...")
        
        # Reset spawners
        for (Spawner : PlayerSpawners):
            # Reset spawner configuration
            Print("Spawner configuration reset")
        
        # Reset item granters
        for (Granter : ItemGranters):
            # Reset granter configuration
            Print("Item granter configuration reset")
        
        # Reset building disabler
        if (Disabler := BuildingDisabler?):
            # Reset building disabler configuration
            Print("Building disabler configuration reset")
        
        # Reset round timer
        if (Timer := RoundTimer?):
            # Reset timer configuration
            Print("Round timer configuration reset")
        
        Print("All device configurations reset")
    
    # Clear all state variables
    ClearAllState(): void =
        Print("Clearing all Box Fight state...")
        
        # Clear player state
        set ActivePlayers = array{}
        set PlayerWins = map{}
        set PlayerLoadouts = map{}
        
        # Clear round state
        set CurrentRound = 0
        set RoundActive = false
        set RoundWinner = false
        set RoundTimeRemaining = 300.0
        
        # Clear draft state
        set IsDrafting = false
        set DraftOrder = array{}
        set CurrentDraftPlayer = 0
        set DraftRound = 0
        set DraftedItems = array{}
        
        # Reset available items
        InitializeDraftSystem()
        
        Print("All Box Fight state cleared")
    
    # Set required devices for the controller (called by game manager)
    SetRequiredDevices(
        Spawners: []player_spawner_device,
        Granters: []item_granter_device,
        Disabler: ?building_disabler_device,
        Timer: ?timer_device,
        Interface: ?ui_device
    ): void =
        set PlayerSpawners = Spawners
        set ItemGranters = Granters
        set BuildingDisabler = Disabler
        set RoundTimer = Timer
        set DraftInterface = Interface
        
        Print("Required devices set for Box Fight Controller")
    
    # Get current round number
    GetCurrentRound(): int = CurrentRound
    
    # Get player win counts
    GetPlayerWins(): [player]int = PlayerWins
    
    # Get current draft round
    GetCurrentDraftRound(): int = DraftRound
    
    # Check if draft is active
    IsDraftActive(): logic = IsDrafting
    
    # Check if round is active
    IsRoundActive(): logic = RoundActive
    
    # Get current round winner
    GetRoundWinner(): ?player = RoundWinner
    
    # Get player loadouts
    GetPlayerLoadouts(): [player][]draft_item = PlayerLoadouts
    
    # Get available draft items
    GetAvailableDraftItems(): []draft_item = AvailableItems
    
    # Get drafted items
    GetDraftedItems(): []draft_item = DraftedItems
    
    # Force draft selection for testing
    ForceDraftSelection(Player: player, ItemName: string): void =
        if (not IsDrafting):
            Print("Cannot force draft selection - draft not active")
            return
        
        # Find item by name
        for (Item : AvailableItems):
            if (Item.ItemName = ItemName and Item.IsAvailable):
                HandleDraftSelection(Player, Item)
                return
        
        Print("Item {ItemName} not found or not available for draft")
    
    # Force round end for testing
    ForceRoundEnd(Winner: ?player): void =
        if (not RoundActive):
            Print("Cannot force round end - no active round")
            return
        
        if (WinnerPlayer := Winner?):
            HandleRoundWin(WinnerPlayer)
        else:
            HandleRoundDraw()
    
    # Emergency reset of box fight state
    EmergencyReset(): void =
        Print("Emergency reset of Box Fight Controller...")
        
        # End mode if active
        if (IsActive):
            EndMode()
        
        # Clear all state
        ClearAllState()
        
        Print("Box Fight Controller emergency reset complete")
    
    # Get random integer in range (helper function)
    GetRandomInt(Min: int, Max: int): int =
        # Simple random number generation
        # In actual implementation, would use proper random number generation
        return Min + ((Max - Min + 1) / 2)  # Placeholder implementation