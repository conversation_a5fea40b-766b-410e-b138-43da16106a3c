using { /Fortnite.com/Devices }
using { /Verse.org/Simulation }
using { /UnrealEngine.com/Temporary/Diagnostics }
using { /Fortnite.com/Characters }
using { /Fortnite.com/Game }

# Import our game types
using { /<EMAIL>/TheTiltedShuffle/game_types }

# Device management system for coordinating UEFN creative devices
# Handles activation/deactivation and configuration of devices based on selected game modes
device_manager := class:
    
    # Device collections organized by type for efficient management
    var PlayerSpawners: []player_spawner_device = array{}
    var ItemGranters: []item_granter_device = array{}
    var TeleporterDevices: []teleporter_device = array{}
    var MutatorZones: []mutator_zone_device = array{}
    var StormController: ?storm_controller_device = false
    var TimerDevices: []timer_device = array{}
    var VotingDevices: []voting_device = array{}
    var HudDevices: []hud_message_device = array{}
    
    # Device state tracking
    var ActiveDevices: []creative_device = array{}
    var DeviceConfigurations: [creative_device]string = map{}
    var ModeDeviceMap: [game_mode][]creative_device = map{}
    
    # Error tracking for device management
    var DeviceErrors: []string = array{}
    var LastValidationResult: logic = false
    
    # Initialize the device manager with all available devices
    Initialize(
        Spawners: []player_spawner_device,
        Granters: []item_granter_device,
        Teleporters: []teleporter_device,
        Mutators: []mutator_zone_device,
        Storm: ?storm_controller_device,
        Timers: []timer_device,
        Voters: []voting_device,
        Huds: []hud_message_device
    ): logic =
        Print("Initializing device manager...")
        
        # Store device references
        set PlayerSpawners = Spawners
        set ItemGranters = Granters
        set TeleporterDevices = Teleporters
        set MutatorZones = Mutators
        set StormController = Storm
        set TimerDevices = Timers
        set VotingDevices = Voters
        set HudDevices = Huds
        
        # Clear previous state
        set ActiveDevices = array{}
        set DeviceConfigurations = map{}
        set DeviceErrors = array{}
        
        # Initialize mode-specific device mappings
        InitializeModeDeviceMappings()
        
        # Validate all devices
        set LastValidationResult = ValidateAllDevices()
        
        if (LastValidationResult):
            Print("Device manager initialized successfully")
        else:
            Print("Device manager initialization completed with errors")
        
        LastValidationResult
    
    # Initialize device mappings for each game mode
    InitializeModeDeviceMappings(): void =
        Print("Initializing mode-specific device mappings...")
        
        # Sniper Arena mode devices
        var SniperDevices: []creative_device = array{}
        set SniperDevices += PlayerSpawners  # All spawners for micro-arenas
        set SniperDevices += TeleporterDevices  # For arena rotation
        # Add specific item granters for sniper rifles and impulse grenades
        if (ItemGranters.Length >= 2):
            set SniperDevices += array{ItemGranters[0], ItemGranters[1]}
        set ModeDeviceMap[game_mode.SniperArena] = SniperDevices
        
        # Gun Game mode devices
        var GunGameDevices: []creative_device = array{}
        set GunGameDevices += PlayerSpawners  # Standard spawns
        set GunGameDevices += ItemGranters  # All granters for weapon progression
        set GunGameDevices += MutatorZones  # All mutator zones
        set ModeDeviceMap[game_mode.GunGameMutators] = GunGameDevices
        
        # Micro BR mode devices
        var MicroBRDevices: []creative_device = array{}
        set MicroBRDevices += PlayerSpawners  # Full map spawns
        if (Storm := StormController?):
            set MicroBRDevices += array{Storm}
        # Limited item granters for loot distribution
        if (ItemGranters.Length >= 3):
            set MicroBRDevices += array{ItemGranters[0], ItemGranters[1], ItemGranters[2]}
        set ModeDeviceMap[game_mode.MicroBR] = MicroBRDevices
        
        # Box Fight mode devices
        var BoxFightDevices: []creative_device = array{}
        # Specific spawners for box fight arenas
        if (PlayerSpawners.Length >= 4):
            set BoxFightDevices += array{PlayerSpawners[0], PlayerSpawners[1], PlayerSpawners[2], PlayerSpawners[3]}
        set BoxFightDevices += ItemGranters  # All granters for draft pool
        set ModeDeviceMap[game_mode.BoxFightDraft] = BoxFightDevices
        
        # Track & Tag mode devices
        var TrackTagDevices: []creative_device = array{}
        set TrackTagDevices += PlayerSpawners  # Standard spawns
        # Specific item granter for juggernaut marker
        if (ItemGranters.Length >= 1):
            set TrackTagDevices += array{ItemGranters[0]}
        set ModeDeviceMap[game_mode.TrackAndTag] = TrackTagDevices
        
        Print("Mode device mappings initialized")
    
    # Validate all devices are properly configured
    ValidateAllDevices(): logic =
        Print("Validating all devices...")
        set DeviceErrors = array{}
        var IsValid: logic = true
        
        # Validate player spawners
        if (PlayerSpawners.Length <= 0):
            set DeviceErrors += array{"No player spawner devices available"}
            set IsValid = false
        else:
            Print("Found {PlayerSpawners.Length} player spawner devices")
        
        # Validate item granters
        if (ItemGranters.Length <= 0):
            set DeviceErrors += array{"No item granter devices available"}
            set IsValid = false
        else:
            Print("Found {ItemGranters.Length} item granter devices")
        
        # Validate teleporter devices
        if (TeleporterDevices.Length <= 0):
            Print("Warning: No teleporter devices available - Sniper Arena mode may not function properly")
        else:
            Print("Found {TeleporterDevices.Length} teleporter devices")
        
        # Validate mutator zones
        if (MutatorZones.Length <= 0):
            Print("Warning: No mutator zone devices available - Gun Game mode may not function properly")
        else:
            Print("Found {MutatorZones.Length} mutator zone devices")
        
        # Validate storm controller
        if (not StormController?):
            Print("Warning: No storm controller available - Micro BR mode may not function properly")
        else:
            Print("Storm controller device found")
        
        # Validate timer devices
        if (TimerDevices.Length <= 0):
            Print("Warning: No timer devices available - some modes may not function properly")
        else:
            Print("Found {TimerDevices.Length} timer devices")
        
        # Validate voting devices
        if (VotingDevices.Length <= 0):
            set DeviceErrors += array{"No voting devices available"}
            set IsValid = false
        else:
            Print("Found {VotingDevices.Length} voting devices")
        
        # Validate HUD devices
        if (HudDevices.Length <= 0):
            set DeviceErrors += array{"No HUD message devices available"}
            set IsValid = false
        else:
            Print("Found {HudDevices.Length} HUD message devices")
        
        # Log validation results
        if (DeviceErrors.Length > 0):
            Print("Device validation errors:")
            for (Error : DeviceErrors):
                Print("- {Error}")
        else:
            Print("All critical devices validated successfully")
        
        IsValid
    
    # Activate devices for a specific game mode
    ActivateDevicesForMode(Mode: game_mode): logic =
        Print("Activating devices for mode: {Mode}")
        
        # First deactivate all currently active devices
        DeactivateAllDevices()
        
        # Get devices required for this mode
        if (RequiredDevices := ModeDeviceMap[Mode]):
            var ActivationSuccess: logic = true
            
            for (Device : RequiredDevices):
                if (ActivateDevice(Device)):
                    set ActiveDevices += array{Device}
                    Print("Activated device: {Device}")
                else:
                    Print("Failed to activate device: {Device}")
                    set ActivationSuccess = false
            
            if (ActivationSuccess):
                Print("Successfully activated all devices for {Mode}")
                return true
            else:
                Print("Some devices failed to activate for {Mode}")
                return false
        else:
            Print("No device mapping found for mode: {Mode}")
            return false
    
    # Deactivate devices for a specific game mode
    DeactivateDevicesForMode(Mode: game_mode): logic =
        Print("Deactivating devices for mode: {Mode}")
        
        if (RequiredDevices := ModeDeviceMap[Mode]):
            var DeactivationSuccess: logic = true
            
            for (Device : RequiredDevices):
                if (DeactivateDevice(Device)):
                    # Remove from active devices list
                    set ActiveDevices = ActiveDevices.RemoveFirstElement(Device)
                    Print("Deactivated device: {Device}")
                else:
                    Print("Failed to deactivate device: {Device}")
                    set DeactivationSuccess = false
            
            if (DeactivationSuccess):
                Print("Successfully deactivated all devices for {Mode}")
                return true
            else:
                Print("Some devices failed to deactivate for {Mode}")
                return false
        else:
            Print("No device mapping found for mode: {Mode}")
            return false
    
    # Activate a single device with error handling and performance profiling
    ActivateDevice(Device: creative_device): logic =
        Print("Activating device: {Device}")
        
        # Check if device is already active
        if (IsDeviceActive(Device)):
            Print("Device already active: {Device}")
            return true
        
        # Use performance profiling for device activation
        var ActivationResult: logic = false
        
        # Profile the device activation performance
        # Note: In actual implementation, would pass reference to performance optimizer
        spawn:
            # Store device configuration if needed
            set DeviceConfigurations[Device] = "active"
            
            # In a real implementation, this would call device-specific activation methods
            # For example: Device.Enable() or Device.Activate()
            
            # Simulate potential device failure
            if (not TestDeviceActivation(Device)):
                Print("Device activation failed: {Device}")
                RegisterDeviceFailure(Device, "Device activation test failed")
                set ActivationResult = false
            else:
                set ActivationResult = true
        
        if (ActivationResult):
            Print("Device activated successfully: {Device}")
        else:
            Print("Device activation failed: {Device}")
        
        ActivationResult
    
    # Test device activation (placeholder for actual device testing)
    TestDeviceActivation(Device: creative_device): logic =
        # In actual implementation, would perform device-specific activation tests
        # For now, simulate activation test with high success rate
        true  # Placeholder - assume most devices activate successfully
    
    # Register a device failure
    RegisterDeviceFailure(Device: creative_device, FailureReason: string): void =
        Print("Registering device failure: {Device} - {FailureReason}")
        
        # Add to failed devices list if not already present
        var AlreadyFailed: logic = false
        for (FailedDevice : FailedDevices):
            if (FailedDevice = Device):
                set AlreadyFailed = true
                break
        
        if (not AlreadyFailed):
            set FailedDevices += array{Device}
        
        # Update failure count for this device
        if (CurrentCount := DeviceFailureCounts[Device]):
            set DeviceFailureCounts[Device] = CurrentCount + 1
        else:
            set DeviceFailureCounts[Device] = 1
        
        # Add to device errors
        set DeviceErrors += array{"Device failure: {Device} - {FailureReason}"}
    
    # Deactivate a single device with error handling
    DeactivateDevice(Device: creative_device): logic =
        Print("Deactivating device: {Device}")
        
        # Check if device is already inactive
        if (not IsDeviceActive(Device)):
            Print("Device already inactive: {Device}")
            return true
        
        # Attempt device deactivation with error handling
        spawn:
            # Remove device configuration
            if (DeviceConfigurations[Device]):
                set DeviceConfigurations = DeviceConfigurations.RemoveKey(Device)
            
            # In a real implementation, this would call device-specific deactivation methods
            # For example: Device.Disable() or Device.Deactivate()
            
            # Simulate potential device failure
            if (not TestDeviceDeactivation(Device)):
                Print("Device deactivation failed: {Device}")
                RegisterDeviceFailure(Device, "Device deactivation test failed")
                return false
        
        Print("Device deactivated successfully: {Device}")
        true
    
    # Test device deactivation (placeholder for actual device testing)
    TestDeviceDeactivation(Device: creative_device): logic =
        # In actual implementation, would perform device-specific deactivation tests
        # For now, simulate deactivation test with high success rate
        true  # Placeholder - assume most devices deactivate successfully
    
    # Deactivate all currently active devices
    DeactivateAllDevices(): logic =
        Print("Deactivating all active devices...")
        var DeactivationSuccess: logic = true
        
        for (Device : ActiveDevices):
            if (not DeactivateDevice(Device)):
                set DeactivationSuccess = false
        
        # Clear active devices list
        set ActiveDevices = array{}
        
        if (DeactivationSuccess):
            Print("All devices deactivated successfully")
        else:
            Print("Some devices failed to deactivate")
        
        DeactivationSuccess
    
    # Apply mode-specific configuration to devices
    ApplyModeConfiguration(Mode: game_mode, Config: mode_config): logic =
        Print("Applying configuration for mode: {Mode}")
        
        # Get devices for this mode
        if (RequiredDevices := ModeDeviceMap[Mode]):
            var ConfigSuccess: logic = true
            
            # Apply configuration based on mode type
            case (Mode):
                game_mode.SniperArena =>
                    ConfigSuccess = ConfigureSniperArenaDevices(RequiredDevices, Config)
                game_mode.GunGameMutators =>
                    ConfigSuccess = ConfigureGunGameDevices(RequiredDevices, Config)
                game_mode.MicroBR =>
                    ConfigSuccess = ConfigureMicroBRDevices(RequiredDevices, Config)
                game_mode.BoxFightDraft =>
                    ConfigSuccess = ConfigureBoxFightDevices(RequiredDevices, Config)
                game_mode.TrackAndTag =>
                    ConfigSuccess = ConfigureTrackTagDevices(RequiredDevices, Config)
                _ =>
                    Print("Unknown mode for configuration: {Mode}")
                    ConfigSuccess = false
            
            if (ConfigSuccess):
                Print("Configuration applied successfully for {Mode}")
            else:
                Print("Configuration failed for {Mode}")
            
            return ConfigSuccess
        else:
            Print("No devices found for mode: {Mode}")
            return false
    
    # Configure devices for Sniper Arena mode
    ConfigureSniperArenaDevices(Devices: []creative_device, Config: mode_config): logic =
        Print("Configuring Sniper Arena devices...")
        
        # Configure spawners for micro-arenas
        var SpawnerCount: int = 0
        for (Device : Devices):
            if (Device isa player_spawner_device):
                # Configure for small arena spawning
                Print("Configuring spawner {SpawnerCount} for micro-arena")
                set SpawnerCount += 1
        
        # Configure teleporters for arena rotation
        var TeleporterCount: int = 0
        for (Device : Devices):
            if (Device isa teleporter_device):
                Print("Configuring teleporter {TeleporterCount} for arena rotation")
                set TeleporterCount += 1
        
        # Configure item granters for sniper rifles and impulse grenades
        var GranterCount: int = 0
        for (Device : Devices):
            if (Device isa item_granter_device):
                if (GranterCount = 0):
                    Print("Configuring granter for sniper rifles")
                else if (GranterCount = 1):
                    Print("Configuring granter for impulse grenades")
                set GranterCount += 1
        
        Print("Sniper Arena device configuration complete")
        true
    
    # Configure devices for Gun Game mode
    ConfigureGunGameDevices(Devices: []creative_device, Config: mode_config): logic =
        Print("Configuring Gun Game devices...")
        
        # Configure spawners for standard gameplay
        for (Device : Devices):
            if (Device isa player_spawner_device):
                Print("Configuring spawner for Gun Game standard spawn")
        
        # Configure mutator zones
        var ZoneCount: int = 0
        for (Device : Devices):
            if (Device isa mutator_zone_device):
                case (ZoneCount):
                    0 => Print("Configuring low gravity zone")
                    1 => Print("Configuring slippery floor zone")
                    2 => Print("Configuring double jump zone")
                    _ => Print("Configuring additional mutator zone {ZoneCount}")
                set ZoneCount += 1
        
        # Configure item granters for weapon progression
        var WeaponLevel: int = 1
        for (Device : Devices):
            if (Device isa item_granter_device):
                Print("Configuring granter for weapon level {WeaponLevel}")
                set WeaponLevel += 1
        
        Print("Gun Game device configuration complete")
        true
    
    # Configure devices for Micro BR mode
    ConfigureMicroBRDevices(Devices: []creative_device, Config: mode_config): logic =
        Print("Configuring Micro BR devices...")
        
        # Configure spawners for full map spawning
        for (Device : Devices):
            if (Device isa player_spawner_device):
                Print("Configuring spawner for Micro BR full map spawn")
        
        # Configure storm controller for accelerated timing
        for (Device : Devices):
            if (Device isa storm_controller_device):
                Print("Configuring storm controller for accelerated 7-minute match")
        
        # Configure limited loot distribution
        var LootTier: int = 1
        for (Device : Devices):
            if (Device isa item_granter_device):
                Print("Configuring loot tier {LootTier} distribution")
                set LootTier += 1
        
        Print("Micro BR device configuration complete")
        true
    
    # Configure devices for Box Fight mode
    ConfigureBoxFightDevices(Devices: []creative_device, Config: mode_config): logic =
        Print("Configuring Box Fight devices...")
        
        # Configure spawners for box fight arenas
        var ArenaNumber: int = 1
        for (Device : Devices):
            if (Device isa player_spawner_device):
                Print("Configuring spawner for box fight arena {ArenaNumber}")
                set ArenaNumber += 1
        
        # Configure item granters for draft pool
        var PoolItem: int = 1
        for (Device : Devices):
            if (Device isa item_granter_device):
                Print("Configuring draft pool item {PoolItem}")
                set PoolItem += 1
        
        Print("Box Fight device configuration complete")
        true
    
    # Configure devices for Track & Tag mode
    ConfigureTrackTagDevices(Devices: []creative_device, Config: mode_config): logic =
        Print("Configuring Track & Tag devices...")
        
        # Configure spawners for standard gameplay
        for (Device : Devices):
            if (Device isa player_spawner_device):
                Print("Configuring spawner for Track & Tag standard spawn")
        
        # Configure item granter for juggernaut marker
        for (Device : Devices):
            if (Device isa item_granter_device):
                Print("Configuring juggernaut marker granter")
                break  # Only need one for the marker
        
        Print("Track & Tag device configuration complete")
        true
    
    # Get devices required for a specific mode
    GetModeDevices(Mode: game_mode): []creative_device =
        if (RequiredDevices := ModeDeviceMap[Mode]):
            RequiredDevices
        else:
            array{}
    
    # Get currently active devices
    GetActiveDevices(): []creative_device = ActiveDevices
    
    # Check if a specific device is active
    IsDeviceActive(Device: creative_device): logic =
        for (ActiveDevice : ActiveDevices):
            if (ActiveDevice = Device):
                return true
        false
    
    # Get device validation errors
    GetDeviceErrors(): []string = DeviceErrors
    
    # Check if device manager is ready for mode activation
    IsReadyForMode(Mode: game_mode): logic =
        # Must have passed validation
        if (not LastValidationResult):
            Print("Device manager not ready - validation failed")
            return false
        
        # Must have devices mapped for the requested mode
        if (not ModeDeviceMap[Mode]?):
            Print("Device manager not ready - no devices mapped for {Mode}")
            return false
        
        # Check that required devices are available
        if (RequiredDevices := ModeDeviceMap[Mode]):
            if (RequiredDevices.Length <= 0):
                Print("Device manager not ready - no devices available for {Mode}")
                return false
        
        true
    
    # Get device manager status
    GetStatus(): string =
        var StatusMessage: string = "Device Manager Status:\n"
        set StatusMessage += "- Active Devices: {ActiveDevices.Length}\n"
        set StatusMessage += "- Validation: {if (LastValidationResult): "PASSED" else: "FAILED"}\n"
        set StatusMessage += "- Errors: {DeviceErrors.Length}\n"
        
        if (DeviceErrors.Length > 0):
            set StatusMessage += "Recent Errors:\n"
            for (Error : DeviceErrors):
                set StatusMessage += "  - {Error}\n"
        
        StatusMessage
    
    # Reset device manager to initial state
    Reset(): void =
        Print("Resetting device manager...")
        
        # Deactivate all devices
        DeactivateAllDevices()
        
        # Clear state
        set ActiveDevices = array{}
        set DeviceConfigurations = map{}
        set DeviceErrors = array{}
        
        # Re-validate devices
        set LastValidationResult = ValidateAllDevices()
        
        Print("Device manager reset complete")
    
    # Emergency shutdown - deactivate everything immediately
    EmergencyShutdown(): void =
        Print("EMERGENCY: Shutting down all devices immediately")
        
        # Force deactivate all devices without error checking
        for (Device : ActiveDevices):
            DeactivateDevice(Device)
        
        # Clear all state
        set ActiveDevices = array{}
        set DeviceConfigurations = map{}
        
        Print("Emergency shutdown complete")